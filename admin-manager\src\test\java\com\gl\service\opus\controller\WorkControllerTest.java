package com.gl.service.opus.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.opus.service.WorkService;
import com.gl.service.opus.vo.MergeVo;
import com.gl.service.opus.vo.SendWorkMusicParams;
import com.gl.service.opus.vo.VoiceWorkVo;
import com.gl.service.opus.vo.dto.WorkDto;
import com.gl.system.vo.SysUserVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;
import org.mockito.MockedStatic;

/**
 * WorkController单元测试类
 * 测试WorkController中的所有REST端点，包括正常流程和异常情况
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WorkController单元测试")
class WorkControllerTest {

    private MockMvc mockMvc;

    @Mock
    private WorkService workService;

    @InjectMocks
    private WorkController workController;

    private ObjectMapper objectMapper;

    // 测试数据
    private VoiceWorkVo validVoiceWorkVo;
    private WorkDto validWorkDto;
    private SpeechSynthesizerDto validSpeechDto;
    private SendWorkMusicParams validSendParams;
    private MergeVo validMergeVo;
    private LoginUser mockLoginUser;
    private SysUserVo mockSysUser;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(workController).build();
        objectMapper = new ObjectMapper();
        setupTestData();
    }

    /**
     * 初始化测试数据
     */
    private void setupTestData() {
        // 创建模拟用户
        mockSysUser = new SysUserVo();
        mockSysUser.setSiteId(1L);
        mockSysUser.setId(1L);
        mockSysUser.setLoginName("testuser");

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockSysUser);

        // 创建有效的VoiceWorkVo
        validVoiceWorkVo = new VoiceWorkVo();
        validVoiceWorkVo.setId(1L);
        validVoiceWorkVo.setTitle("测试作品标题");
        validVoiceWorkVo.setContent("测试作品内容");
        validVoiceWorkVo.setAnchorId(1L);
        validVoiceWorkVo.setSpeed(100);
        validVoiceWorkVo.setVolume(80);

        // 创建有效的WorkDto
        validWorkDto = new WorkDto();
        validWorkDto.setIds(Arrays.asList(1L, 2L));
        validWorkDto.setAnchorId(1L);
        validWorkDto.setSearchCondition("测试");

        // 创建有效的SpeechSynthesizerDto
        validSpeechDto = new SpeechSynthesizerDto();
        validSpeechDto.setAnchorId(1L);
        validSpeechDto.setText("测试文本内容");
        validSpeechDto.setVolume(80);
        validSpeechDto.setSpeechRate(100);

        // 创建有效的SendWorkMusicParams
        validSendParams = new SendWorkMusicParams();
        validSendParams.setId(1L);
        validSendParams.setDeviceIds(Arrays.asList(1L, 2L));
        validSendParams.setFileUrl("https://test.com/music.mp3");
        validSendParams.setTitle("测试音乐");

        // 创建有效的MergeVo
        validMergeVo = new MergeVo();
        validMergeVo.setAudio(Arrays.asList("audio1.mp3", "audio2.mp3"));
        validMergeVo.setText("合并音频文本");
        validMergeVo.setBgm("bgm.mp3");
    }

    // ==================== 基础测试 ====================

    @Test
    @DisplayName("测试基础设置")
    void testBasicSetup() {
        // 验证基本对象不为空
        assertNotNull(workController);
        assertNotNull(workService);
        assertNotNull(mockMvc);
        assertNotNull(objectMapper);
    }

    // ==================== GET /work 测试 ====================

    @Test
    @DisplayName("测试GET /work - 成功获取作品列表")
    void testList_Success() throws Exception {
        // Given
        when(workService.list(any(WorkDto.class), eq(1))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work")
                .param("anchorId", "1")
                .param("searchCondition", "测试"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).list(any(WorkDto.class), eq(1));
    }

    @Test
    @DisplayName("测试GET /work - 无参数时也能正常返回")
    void testList_NoParameters() throws Exception {
        // Given
        when(workService.list(any(WorkDto.class), eq(1))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).list(any(WorkDto.class), eq(1));
    }

    // ==================== POST /work 测试 ====================

    @Test
    @DisplayName("测试POST /work - 成功新增作品")
    void testAdd_Success() throws Exception {
        // Given
        when(workService.add(any(VoiceWorkVo.class))).thenReturn(Result.success());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(validVoiceWorkVo)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(10000));

            verify(workService).add(any(VoiceWorkVo.class));
        }
    }

    @Test
    @DisplayName("测试POST /work - 请求体为空时返回400")
    void testAdd_EmptyBody() throws Exception {
        // Given
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content("{}"))
                    .andExpect(status().isOk()); // Spring会处理空对象，不会返回400

            verify(workService).add(any(VoiceWorkVo.class));
        }
    }

    // ==================== POST /work/uploadWork 测试 ====================

    @Test
    @DisplayName("测试POST /work/uploadWork - 成功上传作品")
    void testUploadWork_Success() throws Exception {
        // Given
        when(workService.uploadWork(any(VoiceWorkVo.class))).thenReturn(Result.success());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work/uploadWork")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(validVoiceWorkVo)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(10000));

            verify(workService).uploadWork(any(VoiceWorkVo.class));
        }
    }

    // ==================== POST /work/sendWorkMusic 测试 ====================

    @Test
    @DisplayName("测试POST /work/sendWorkMusic - 成功发送作品到设备")
    void testSendWorkMusic_Success() throws Exception {
        // Given
        when(workService.sendWorkMusic(any(SendWorkMusicParams.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/work/sendWorkMusic")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validSendParams)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).sendWorkMusic(any(SendWorkMusicParams.class));
    }

    // ==================== PUT /work 测试 ====================

    @Test
    @DisplayName("测试PUT /work - 成功更新作品")
    void testUpdate_Success() throws Exception {
        // Given
        when(workService.update(any(VoiceWorkVo.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(put("/work")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validVoiceWorkVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).update(any(VoiceWorkVo.class));
    }

    // ==================== DELETE /work 测试 ====================

    @Test
    @DisplayName("测试DELETE /work - 成功删除作品")
    void testDelete_Success() throws Exception {
        // Given
        when(workService.delete(any(WorkDto.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(delete("/work")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validWorkDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).delete(any(WorkDto.class));
    }

    // ==================== POST /work/export 测试 ====================

    @Test
    @DisplayName("测试POST /work/export - 成功导出作品列表")
    void testExportList_Success() throws Exception {
        // Given
        doNothing().when(workService).exportList(any(WorkDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/work/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validWorkDto)))
                .andExpect(status().isOk());

        verify(workService).exportList(any(WorkDto.class), any(HttpServletResponse.class));
    }

    // ==================== GET /work/templates 测试 ====================

    @Test
    @DisplayName("测试GET /work/templates - 成功获取模板列表")
    void testFindTemplates_Success() throws Exception {
        // Given
        when(workService.findTemplates()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/templates"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).findTemplates();
    }

    // ==================== GET /work/anchors 测试 ====================

    @Test
    @DisplayName("测试GET /work/anchors - 成功获取主播列表")
    void testFindAnchors_Success() throws Exception {
        // Given
        when(workService.findAnchors(anyString())).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/anchors")
                .param("searchCondition", "测试主播"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).findAnchors("测试主播");
    }

    @Test
    @DisplayName("测试GET /work/anchors - 无搜索条件时也能正常返回")
    void testFindAnchors_NoSearchCondition() throws Exception {
        // Given
        when(workService.findAnchors(isNull())).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/anchors"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).findAnchors(isNull());
    }

    // ==================== POST /work/audition 测试 ====================

    @Test
    @DisplayName("测试POST /work/audition - 成功试听")
    void testAudition_Success() throws Exception {
        // Given
        when(workService.audition(any(SpeechSynthesizerDto.class))).thenReturn(Result.success());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work/audition")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(validSpeechDto)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(10000));

            verify(workService).audition(any(SpeechSynthesizerDto.class));
        }
    }

    @Test
    @DisplayName("测试POST /work/audition - 服务返回失败时正确处理")
    void testAudition_ServiceFailure() throws Exception {
        // Given
        when(workService.audition(any(SpeechSynthesizerDto.class)))
                .thenReturn(Result.fail("试听失败"));

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work/audition")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(validSpeechDto)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500));

            verify(workService).audition(any(SpeechSynthesizerDto.class));
        }
    }

    // ==================== GET /work/getWorkList 测试 ====================

    @Test
    @DisplayName("测试GET /work/getWorkList - 成功获取作品列表")
    void testGetWorkList_Success() throws Exception {
        // Given
        when(workService.getWorkList()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/getWorkList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).getWorkList();
    }

    // ==================== POST /work/longCompound 测试 ====================

    @Test
    @DisplayName("测试POST /work/longCompound - 成功长文本合成")
    void testLongCompound_Success() throws Exception {
        // Given
        when(workService.longCompound(any(SpeechSynthesizerDto.class))).thenReturn(Result.success());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When & Then
            mockMvc.perform(post("/work/longCompound")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(validSpeechDto)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(10000));

            verify(workService).longCompound(any(SpeechSynthesizerDto.class));
        }
    }

    // ==================== POST /work/dialog 测试 ====================

    @Test
    @DisplayName("测试POST /work/dialog - 成功对话合成")
    void testDialog_Success() throws Exception {
        // Given
        when(workService.dialog(any(SpeechSynthesizerDto.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/work/dialog")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validSpeechDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).dialog(any(SpeechSynthesizerDto.class));
    }

    // ==================== POST /work/mergeAudio 测试 ====================

    @Test
    @DisplayName("测试POST /work/mergeAudio - 成功多人配音")
    void testMergeAudio_Success() throws Exception {
        // Given
        when(workService.mergeAudio(any(MergeVo.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/work/mergeAudio")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validMergeVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).mergeAudio(any(MergeVo.class));
    }

    // ==================== GET /work/aiStyleList 测试 ====================

    @Test
    @DisplayName("测试GET /work/aiStyleList - 成功获取AI风格列表")
    void testAiStyleList_Success() throws Exception {
        // Given
        when(workService.aiStyleList()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/aiStyleList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).aiStyleList();
    }

    // ==================== GET /work/aiLanguageList 测试 ====================

    @Test
    @DisplayName("测试GET /work/aiLanguageList - 成功获取AI语言列表")
    void testAiLanguageList_Success() throws Exception {
        // Given
        when(workService.aiLanguageList()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/aiLanguageList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).aiLanguageList();
    }

    // ==================== GET /work/aiClassList 测试 ====================

    @Test
    @DisplayName("测试GET /work/aiClassList - 成功获取AI分类列表")
    void testAiClassList_Success() throws Exception {
        // Given
        when(workService.aiClassList()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/aiClassList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).aiClassList();
    }

    // ==================== POST /work/uploadVideo 测试 ====================

    @Test
    @DisplayName("测试POST /work/uploadVideo - 成功上传视频")
    void testUploadVideo_Success() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.mp4", "video/mp4", "test video content".getBytes());
        when(workService.uploadVideo(any(), anyString())).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.multipart("/work/uploadVideo")
                .file(file)
                .param("name", "测试视频"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).uploadVideo(any(), eq("测试视频"));
    }

    // ==================== GET /work/myMusic 测试 ====================

    @Test
    @DisplayName("测试GET /work/myMusic - 成功获取我的音乐")
    void testMyMusic_Success() throws Exception {
        // Given
        when(workService.myMusic()).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(get("/work/myMusic"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(workService).myMusic();
    }

    // ==================== 异常情况测试 ====================

    @Test
    @DisplayName("测试POST请求 - 无效JSON格式时返回400")
    void testPost_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/work")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());

        verify(workService, never()).add(any(VoiceWorkVo.class));
    }

    @Test
    @DisplayName("测试服务层异常 - 正确传播异常")
    void testServiceException() throws Exception {
        // Given
        when(workService.list(any(WorkDto.class), eq(1)))
                .thenThrow(new RuntimeException("服务异常"));

        // When & Then
        mockMvc.perform(get("/work"))
                .andExpect(status().is5xxServerError());

        verify(workService).list(any(WorkDto.class), eq(1));
    }
}
